import { useEffect, useRef, useState } from 'react';
import style from '../../assets/styles/ChatPage.module.css';
import ChatInput from '../../components/specific/ChatInput/ChatInput';
import botIcon from '../../assets/images/logoIcon.png';
import senderIcon from '../../assets/images/svg/defaultProfile.svg';
import { useLocation } from 'react-router-dom';
import { useUploadFileMutation } from '../../services/chatServices';
import useLocalStorage from '../../hooks/useLocalStorage';
import { useDispatch } from 'react-redux';
import ChatHeader from '../../components/chat/ChatHeader';
import MessageList from '../../components/chat/MessageList';
import { useGetProjectByIdQuery } from '../../services/projectsService';
import LoadingChat from '../../assets/loader/LoadingChat';
import { useProfileImage } from '../../hooks/useProfileImage';
import {
  setIsProjectWindowOpen,
  setProjectDataId,
} from '../../store/slices/projectSlice';
import { complianceService } from '../../services/complianceServices';
import { getFileIcon } from '../../utils/getFileExtention';
import TourButton from '../../components/common/TourButton';

import { useChat, useFileUpload, useSelections } from '../../hooks/chat';

const ChatPage = () => {
  const location = useLocation();
  const {
    summary,
    file,
    numberOfPages,
    projectId,
    questions,
    uploadFileTitle,
    inputMessage,
    pageRange,
    recentProject,
    insightProjectId,
    customPromptContent,
  } = location.state || {};

  const dispatch = useDispatch();
  const [user] = useLocalStorage('user', null);
  const { getChatThumbnail } = useProfileImage(user);
  const chatContentRef = useRef<HTMLDivElement>(null);
  const [mongoProjectId, setMongoProjectId] = useState<string>('');
  const [currentPageRange, setCurrentPageRange] = useState<string>('');

  // API hooks
  const [uploadFile] = useUploadFileMutation();
  const {
    data: projectData,
    isLoading: isProjectLoading,
    error: projectError,
  } = useGetProjectByIdQuery(recentProject?.id, {
    skip: !recentProject?.id,
  });

  // Derived values with null checks
  const recentPageRange = projectData?.data?.response?.length
    ? projectData.data.response[projectData.data.response.length - 1]?.page_no
    : pageRange;

  // Initialize page range state
  useEffect(() => {
    const storedPageRange = localStorage.getItem('pageRangeChanges');
    const initialPageRange =
      storedPageRange !== null && storedPageRange !== 'undefined'
        ? storedPageRange
        : recentPageRange || '';
    setCurrentPageRange(initialPageRange);
  }, [recentPageRange]);

  const projectID = projectData?.data?.project_id || projectId;
  const getFileName = file?.name || projectData?.data?.file_name;
  const getFileType = file?.type || projectData?.data?.file_type;
  const getProjectTitle = projectData?.data?.title || uploadFileTitle;

  // Replace the newProfileImage calculation with thumbnail
  const userThumbnail = getChatThumbnail(user);

  // Custom hooks
  const {
    messages,
    setMessages,
    isProcessing,
    inputValue,
    setInputValue,
    currentStreamingMessageId,
    loadingFinalResponse,
    codeInput,
    isLongResponse,
    handleSendMessage,
    addMessage,
  } = useChat({
    userInLocalStorage: user,
    projectID,
    getFileName,
    getFileType,
    getProjectTitle,
    pageRangeChanges: currentPageRange || '',
    newProfileImage: userThumbnail,
    senderIcon,
    botIcon,
    onMongoProjectIdChange: (id) => setMongoProjectId(id),
    projectData: projectData?.data,
    handleOnQuestionClick: (question) => handleSendMessage(question, ''),
  });

  const {
    isProcessing: isFileProcessing,
    uploadFileWithinChat,
    handleFileUpload,
  } = useFileUpload({
    userInLocalStorage: user,
    projectID,
    pageRangeChanges: currentPageRange || '',
    newProfileImage: userThumbnail,
    senderIcon,
    botIcon,
    addMessage,
    setMessages,
    uploadFile,
    dispatch,
    complianceService,
    setIsProjectWindowOpen,
  });

  const {
    selectedCharts,
    selectedImages,
    selectedTexts,
    handleSelectChart,
    handleSelectImage,
    handleSelectText,
  } = useSelections({
    mongoProjectId: mongoProjectId || projectData?.data?.id || '',
  });

  // Effects
  useEffect(() => {
    if (chatContentRef.current) {
      chatContentRef.current.scrollTop = chatContentRef.current.scrollHeight;
    }
  }, [messages]);

  useEffect(() => {
    if (inputMessage) {
      dispatch(complianceService.util.invalidateTags(['Files']));
      dispatch(setIsProjectWindowOpen(false));
    }
  }, [inputMessage]);

  useEffect(() => {
    if (inputMessage?.length > 0 && !projectData?.data) {
      handleSendMessage(inputMessage, 'Untitled');
    }
  }, []);

  useEffect(() => {
    if (location.pathname === '/chat') {
      if (insightProjectId) {
        dispatch(setProjectDataId(insightProjectId));
        dispatch(setIsProjectWindowOpen(true));
      } else {
        dispatch(setProjectDataId(projectData?.data?.id));
        dispatch(setIsProjectWindowOpen(true));
      }
    }
    return () => {
      dispatch(setProjectDataId(''));
      dispatch(setIsProjectWindowOpen(false));
    };
  }, [projectData?.data?.id, insightProjectId]);

  // Initial messages setup for file upload
  useEffect(() => {
    if (file && summary) {
      const initialMessages = [
        {
          sender: 'user',
          content: customPromptContent !== '' ? customPromptContent : file.name,
          preDefinedPrompt: customPromptContent !== '' ? file.name : '',
          type: 'light',
          icon: getFileIcon(file.name),
          image: userThumbnail || senderIcon,
          timestamp: new Date().toLocaleTimeString(),
          noOfPages: numberOfPages,
        },
        {
          type: 'light',
          sender: 'bot',
          content: summary,
          image: botIcon,
          timestamp: new Date().toLocaleTimeString(),
        },
      ];

      if (questions) {
        const questionMessages = questions.map((item: string) => ({
          handleOnClick: () => handleSendMessage(item, ''),
          type: 'dark',
          sender: 'bot',
          content: item,
          timestamp: new Date().toLocaleTimeString(),
        }));
        initialMessages.push(...questionMessages);
      }

      setMessages(initialMessages);
    }
  }, [file, summary]);

  // Page range handlers
  const handlePageRangeChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setCurrentPageRange(event.target.value);
  };

  const handleSavePageRange = () => {
    localStorage.setItem('pageRangeChanges', currentPageRange);
    console.log('Page range saved:', currentPageRange);
  };

  const handleComplianceClick = () => {
    // First ensure we have the project ID set
    dispatch(setProjectDataId(projectData?.data?.id || mongoProjectId));
    // Open the project window (sidebar)
    dispatch(setIsProjectWindowOpen(true));
    // Dispatch an event to scroll to compliance section
    const event = new CustomEvent('scrollToCompliance');
    window.dispatchEvent(event);
  };

  // Loading states
  if (isProjectLoading) {
    return (
      <div className={style.loadingChatContainer}>
        <LoadingChat />
      </div>
    );
  }

  if (projectError) {
    return (
      <div className={style.loadingChatContainer}>
        Error loading project data
      </div>
    );
  }

  return (
    <div id="mainChatContainer" className={style.mainChatContainer}>
      <ChatHeader
        recentProjectTitle={recentProject?.title}
        projectID={projectID}
        sketchbookID={projectData?.data?.id || mongoProjectId}
        uploadFileTitle={uploadFileTitle}
        handleOpenDialog={() => {}}
        selectedCharts={selectedCharts}
        selectedImages={selectedImages}
        recentProjects={projectData?.data}
        pageRangeChanges={recentPageRange}
        handlePageRangeChange={() => {}}
        handleSavePageRange={() => {
          console.log('Saving page range...');
        }}
        selectedTexts={selectedTexts}
        onComplianceClick={handleComplianceClick}
      />
      <div className={style.chatContainer}>
        <div className={style.chatContent} ref={chatContentRef}>
          <MessageList
            loadingFinalResponse={loadingFinalResponse}
            codeInput={codeInput}
            isLongResponse={isLongResponse}
            currentStreamingMessageId={currentStreamingMessageId}
            uploadedFile={uploadFileWithinChat || file}
            messages={messages}
            selectedCharts={selectedCharts}
            selectedImages={selectedImages}
            selectedTexts={selectedTexts}
            handleSelectChart={handleSelectChart}
            handleSelectImage={handleSelectImage}
            handleSelectText={handleSelectText}
            onSendSelected={setInputValue}
          />
        </div>
        <div className={style.inputContainer}>
          <div className={style.inputInnerContainer}>
            <ChatInput
              autofocus={true}
              type="inputWithFileUpload"
              placeholder={
                isProcessing || isFileProcessing
                  ? 'Processing...'
                  : 'Type a message or drop a file...'
              }
              value={inputValue}
              onChange={(e: any) => setInputValue(e.target.value)}
              onSend={() => handleSendMessage(inputValue)}
              onFileUpload={handleFileUpload}
              disabled={isProcessing || isFileProcessing}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatPage;
